import psycopg2
import json
from typing import Dict, Any, List
from decouple import config
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('user_device_details_migration.log'),
        logging.StreamHandler()
    ]
)

# Configuration for main project database (source)
MAIN_DB_CONFIG = {
    'dbname': config('SWADESIC_DB_NAME'),
    'user': config('SWADESIC_DB_USER'),
    'password': config('SWADESIC_DB_PASSWORD'),
    'host': config('SWADESIC_DB_HOST'),
    'port': config('SWADESIC_DB_PORT')
}

# Configuration for chat-backend database (destination)
CHAT_DB_CONFIG = {
    'dbname': config('DB_NAME'),
    'user': config('DB_USER'),
    'password': config('DB_PASSWORD'),
    'host': config('DB_HOST'),
    'port': config('DB_PORT')
}

def connect_to_db(db_config: Dict[str, str]):
    """Connect to database with given configuration"""
    try:
        conn = psycopg2.connect(**db_config)
        return conn
    except Exception as e:
        logging.error(f"Error connecting to database: {str(e)}")
        raise

def fetch_user_device_details(conn):
    """Fetch user device details from main project database"""
    cursor = conn.cursor()
    try:
        cursor.execute("""
            SELECT 
                user_reference,
                device_id,
                fcm_token,
                user_app_version
            FROM "user"."user_device_details"
            WHERE user_reference IS NOT NULL
        """)
        return cursor.fetchall()
    except Exception as e:
        logging.error(f"Error fetching user device details: {str(e)}")
        raise
    finally:
        cursor.close()

def check_user_exists(conn, user_reference: str) -> bool:
    """Check if user exists in chat-backend database"""
    cursor = conn.cursor()
    try:
        cursor.execute("""
            SELECT 1 FROM users 
            WHERE user_reference = %s
        """, (user_reference,))
        return cursor.fetchone() is not None
    except Exception as e:
        logging.error(f"Error checking user existence: {str(e)}")
        return False
    finally:
        cursor.close()

def insert_user_device_details(conn, device_details: List[tuple]):
    """Insert user device details into chat-backend database"""
    cursor = conn.cursor()
    successful_inserts = 0
    failed_inserts = 0
    
    try:
        for user_reference, device_id, fcm_token, user_app_version in device_details:
            try:
                # Check if user exists in chat-backend
                if not check_user_exists(conn, user_reference):
                    logging.warning(f"User {user_reference} not found in chat-backend, skipping device details")
                    failed_inserts += 1
                    continue
                
                # Check if device details already exist
                cursor.execute("""
                    SELECT 1 FROM user_device_details 
                    WHERE user_reference = %s AND device_id = %s
                """, (user_reference, device_id))
                
                if cursor.fetchone():
                    logging.info(f"Device details already exist for user {user_reference}, device {device_id}")
                    continue
                
                # Insert device details
                cursor.execute("""
                    INSERT INTO user_device_details 
                    (user_reference, device_id, fcm_token, user_app_version)
                    VALUES (%s, %s, %s, %s)
                """, (user_reference, device_id, fcm_token, user_app_version))
                
                successful_inserts += 1
                logging.info(f"Successfully inserted device details for user {user_reference}")
                
            except Exception as e:
                logging.error(f"Error inserting device details for user {user_reference}: {str(e)}")
                failed_inserts += 1
                continue
        
        conn.commit()
        logging.info(f"Migration completed. Successful: {successful_inserts}, Failed: {failed_inserts}")
        
    except Exception as e:
        conn.rollback()
        logging.error(f"Error during batch insert: {str(e)}")
        raise
    finally:
        cursor.close()

def main():
    """Main migration function"""
    logging.info("Starting user device details migration...")
    
    main_conn = None
    chat_conn = None
    
    try:
        # Connect to both databases
        logging.info("Connecting to main project database...")
        main_conn = connect_to_db(MAIN_DB_CONFIG)
        
        logging.info("Connecting to chat-backend database...")
        chat_conn = connect_to_db(CHAT_DB_CONFIG)
        
        # Fetch user device details from main database
        logging.info("Fetching user device details from main database...")
        device_details = fetch_user_device_details(main_conn)
        logging.info(f"Found {len(device_details)} device detail records")
        
        if not device_details:
            logging.info("No device details found to migrate")
            return
        
        # Insert into chat-backend database
        logging.info("Inserting device details into chat-backend database...")
        insert_user_device_details(chat_conn, device_details)
        
        logging.info("Migration completed successfully!")
        
    except Exception as e:
        logging.error(f"Migration failed: {str(e)}")
        raise
    finally:
        if main_conn:
            main_conn.close()
        if chat_conn:
            chat_conn.close()

if __name__ == "__main__":
    main()
